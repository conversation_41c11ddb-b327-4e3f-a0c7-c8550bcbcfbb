{"name": "node-odata-project", "version": "1.0.0", "description": "ECS Fargate Node.js service that handles trax db connections", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js"}, "keywords": ["aws", "fargate", "ecs", "metrics", "cloudwatch", "autoscaling", "nodejs"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^8.1.0", "sequelize": "^6.37.7"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}}